2025-07-16 10:16:42.145 | RSA private key for plugin signing not found (this is normal for most services)
2025-07-16 10:16:42.150 | Loaded RSA public key for plugin verification
2025-07-16 10:16:42.318 | GitHub repositories enabled in configuration
2025-07-16 10:16:42.329 | Attempting to connect to RabbitMQ (attempt 1/20)...
2025-07-16 10:16:42.330 | Using RabbitMQ URL: amqp://stage7:stage7password@rabbitmq:5672
2025-07-16 10:16:42.334 | Attempting to connect to RabbitMQ host: rabbitmq
2025-07-16 10:16:42.335 | Connecting to RabbitMQ at amqp://stage7:stage7password@rabbitmq:5672
2025-07-16 10:16:42.341 | Attempting to register with <PERSON> (attempt 1/10)...
2025-07-16 10:16:42.341 | Using Consul URL: consul:8500
2025-07-16 10:16:42.435 | Successfully initialized repository of type: local
2025-07-16 10:16:42.435 | Successfully initialized repository of type: mongo
2025-07-16 10:16:42.436 | Successfully initialized repository of type: librarian-definition
2025-07-16 10:16:42.437 | Successfully initialized repository of type: git
2025-07-16 10:16:42.437 | Initializing GitHub repository with provided credentials
2025-07-16 10:16:42.438 | GitHubRepository: Initialized for cpravetz/s7plugins. Plugins dir: 'plugins'. Default branch from config/env: main
2025-07-16 10:16:42.438 | Successfully initialized repository of type: github
2025-07-16 10:16:42.441 | Refreshing plugin cache...
2025-07-16 10:16:42.441 | Loading plugins from local repository...
2025-07-16 10:16:42.441 | LocalRepo: Loading fresh plugin list
2025-07-16 10:16:42.441 | LocalRepo: Loading from  /usr/src/app/services//capabilitiesmanager/src/plugins
2025-07-16 10:16:42.449 | Refreshing plugin cache...
2025-07-16 10:16:42.450 | Loading plugins from local repository...
2025-07-16 10:16:42.451 | LocalRepo: Loading fresh plugin list
2025-07-16 10:16:42.452 | LocalRepo: Loading from  /usr/src/app/services//capabilitiesmanager/src/plugins
2025-07-16 10:16:42.482 | [shouldBypassAuth] Bypassing auth for auth path: /v1/agent/service/register (matched /register)
2025-07-16 10:16:42.536 | LocalRepo: Loading from  [
2025-07-16 10:16:42.536 |   'ACCOMPLISH',
2025-07-16 10:16:42.536 |   'API_CLIENT',
2025-07-16 10:16:42.536 |   'CHAT',
2025-07-16 10:16:42.536 |   'CODE_EXECUTOR',
2025-07-16 10:16:42.536 |   'DATA_TOOLKIT',
2025-07-16 10:16:42.536 |   'FILE_OPS_PYTHON',
2025-07-16 10:16:42.536 |   'GET_USER_INPUT',
2025-07-16 10:16:42.536 |   'SCRAPE',
2025-07-16 10:16:42.536 |   'SEARCH_PYTHON',
2025-07-16 10:16:42.536 |   'TASK_MANAGER',
2025-07-16 10:16:42.536 |   'TEXT_ANALYSIS',
2025-07-16 10:16:42.536 |   'WEATHER'
2025-07-16 10:16:42.536 | ]
2025-07-16 10:16:42.536 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-16 10:16:42.539 | LocalRepo: Loading from  [
2025-07-16 10:16:42.539 |   'ACCOMPLISH',
2025-07-16 10:16:42.539 |   'API_CLIENT',
2025-07-16 10:16:42.539 |   'CHAT',
2025-07-16 10:16:42.539 |   'CODE_EXECUTOR',
2025-07-16 10:16:42.539 |   'DATA_TOOLKIT',
2025-07-16 10:16:42.539 |   'FILE_OPS_PYTHON',
2025-07-16 10:16:42.539 |   'GET_USER_INPUT',
2025-07-16 10:16:42.539 |   'SCRAPE',
2025-07-16 10:16:42.539 |   'SEARCH_PYTHON',
2025-07-16 10:16:42.539 |   'TASK_MANAGER',
2025-07-16 10:16:42.539 |   'TEXT_ANALYSIS',
2025-07-16 10:16:42.539 |   'WEATHER'
2025-07-16 10:16:42.539 | ]
2025-07-16 10:16:42.540 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-16 10:16:42.634 | [shouldBypassAuth] Bypassing auth for auth path: /registerComponent (matched /register)
2025-07-16 10:16:42.681 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-16 10:16:42.681 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-16 10:16:42.696 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-16 10:16:42.699 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-16 10:16:42.701 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-16 10:16:42.701 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-16 10:16:42.705 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-16 10:16:42.705 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-16 10:16:42.705 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-16 10:16:42.705 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-16 10:16:42.708 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-16 10:16:42.712 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-16 10:16:42.723 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-16 10:16:42.723 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-16 10:16:42.723 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-16 10:16:42.723 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-16 10:16:42.723 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-16 10:16:42.724 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-16 10:16:42.728 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-16 10:16:42.728 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-16 10:16:42.728 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-16 10:16:42.734 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-16 10:16:42.737 | LocalRepo: Locators count 12
2025-07-16 10:16:42.737 | LocalRepo: Locators count 12
2025-07-16 10:16:42.742 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-16 10:16:42.742 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-16 10:16:42.745 | LocalRepository.fetch: Cache hit for id 'plugin-API_CLIENT' at /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-16 10:16:42.749 | LocalRepository.fetch: Cache hit for id 'plugin-API_CLIENT' at /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-16 10:16:42.753 | LocalRepository.fetch: Cache hit for id 'plugin-CHAT' at /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-16 10:16:42.753 | LocalRepository.fetch: Cache hit for id 'plugin-CHAT' at /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-16 10:16:42.753 | LocalRepository.fetch: Cache hit for id 'plugin-CODE_EXECUTOR' at /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-16 10:16:42.756 | LocalRepository.fetch: Cache hit for id 'plugin-CODE_EXECUTOR' at /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-16 10:16:42.756 | LocalRepository.fetch: Cache hit for id 'plugin-DATA_TOOLKIT' at /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-16 10:16:42.758 | LocalRepository.fetch: Cache hit for id 'plugin-FILE_OPS_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-16 10:16:42.764 | LocalRepository.fetch: Cache hit for id 'plugin-ASK_USER_QUESTION' at /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-16 10:16:42.765 | LocalRepository.fetch: Cache hit for id 'plugin-SCRAPE' at /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-16 10:16:42.765 | LocalRepository.fetch: Cache hit for id 'plugin-DATA_TOOLKIT' at /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-16 10:16:42.765 | LocalRepository.fetch: Cache hit for id 'plugin-SEARCH_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-16 10:16:42.765 | LocalRepository.fetch: Cache hit for id 'plugin-FILE_OPS_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-16 10:16:42.765 | LocalRepository.fetch: Cache hit for id 'plugin-TASK_MANAGER' at /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-16 10:16:42.769 | LocalRepository.fetch: Cache hit for id 'plugin-ASK_USER_QUESTION' at /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-16 10:16:42.769 | LocalRepository.fetch: Cache hit for id 'plugin-TEXT_ANALYSIS' at /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-16 10:16:42.769 | LocalRepository.fetch: Cache hit for id 'plugin-SCRAPE' at /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-16 10:16:42.769 | LocalRepository.fetch: Cache hit for id 'plugin-WEATHER' at /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-16 10:16:42.769 | Loaded 12 plugins from local repository
2025-07-16 10:16:42.769 | Loading plugins from mongo repository...
2025-07-16 10:16:42.782 | LocalRepository.fetch: Cache hit for id 'plugin-SEARCH_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-16 10:16:42.797 | LocalRepository.fetch: Cache hit for id 'plugin-TASK_MANAGER' at /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-16 10:16:42.797 | LocalRepository.fetch: Cache hit for id 'plugin-TEXT_ANALYSIS' at /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-16 10:16:42.797 | LocalRepository.fetch: Cache hit for id 'plugin-WEATHER' at /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-16 10:16:42.797 | Loaded 12 plugins from local repository
2025-07-16 10:16:42.797 | Loading plugins from mongo repository...
2025-07-16 10:16:42.849 | [AuthenticatedAxios] Request doyobojuq5j: Failed after 76ms: {
2025-07-16 10:16:42.849 |   status: undefined,
2025-07-16 10:16:42.849 |   statusText: undefined,
2025-07-16 10:16:42.849 |   data: undefined,
2025-07-16 10:16:42.849 |   url: 'http://librarian:5040/searchData'
2025-07-16 10:16:42.849 | }
2025-07-16 10:16:43.943 | Loaded 0 plugins from mongo repository
2025-07-16 10:16:43.943 | Loading plugins from librarian-definition repository...
2025-07-16 10:16:43.980 | Loaded 0 plugins from mongo repository
2025-07-16 10:16:43.980 | Loading plugins from librarian-definition repository...
2025-07-16 10:16:44.016 | Loaded 0 plugins from librarian-definition repository
2025-07-16 10:16:44.016 | Loading plugins from git repository...
2025-07-16 10:16:44.029 | Loaded 0 plugins from librarian-definition repository
2025-07-16 10:16:44.030 | Loading plugins from git repository...
2025-07-16 10:16:44.135 | Failed to list plugins from Git repository: Cloning into '/usr/src/app/services/capabilitiesmanager/temp/list-plugins'...
2025-07-16 10:16:44.135 | fatal: cannot copy '/usr/share/git-core/templates/info/exclude' to '/usr/src/app/services/capabilitiesmanager/temp/list-plugins/.git/info/exclude': File exists
2025-07-16 10:16:44.135 | 
2025-07-16 10:16:44.138 | Failed to list plugins from Git repository: Cloning into '/usr/src/app/services/capabilitiesmanager/temp/list-plugins'...
2025-07-16 10:16:44.138 | /usr/src/app/services/capabilitiesmanager/temp/list-plugins/.git/hooks/: No such file or directory
2025-07-16 10:16:44.138 | 
2025-07-16 10:16:44.142 | Loaded 0 plugins from git repository
2025-07-16 10:16:44.142 | Loading plugins from github repository...
2025-07-16 10:16:47.311 | Loaded 0 plugins from git repository
2025-07-16 10:16:47.311 | Loading plugins from github repository...
2025-07-16 10:16:47.565 | Service CapabilitiesManager registered with Consul
2025-07-16 10:16:47.565 | Successfully registered CapabilitiesManager with Consul
2025-07-16 10:16:47.581 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 401. Details: {"message":"Bad credentials","documentation_url":"https://docs.github.com/rest","status":"401"}
2025-07-16 10:16:47.581 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:157:31)
2025-07-16 10:16:47.581 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-16 10:16:47.581 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:364:30)
2025-07-16 10:16:47.581 |     at async PluginRegistry.refreshCache (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:267:37)
2025-07-16 10:16:47.581 |     at async PluginRegistry.initialize (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:113:13)
2025-07-16 10:16:47.581 | GitHubRepository: Authentication failed. Please check GITHUB_TOKEN and repository permissions.
2025-07-16 10:16:47.581 | Loaded 0 plugins from github repository
2025-07-16 10:16:47.582 | Plugin cache refreshed. Total plugins: 12
2025-07-16 10:16:47.582 | PluginRegistry initialized and cache populated.
2025-07-16 10:16:47.582 | PluginRegistry: Registered verbs after cache refresh: [
2025-07-16 10:16:47.582 |   'ACCOMPLISH',
2025-07-16 10:16:47.582 |   'API_CLIENT',
2025-07-16 10:16:47.582 |   'CHAT',
2025-07-16 10:16:47.583 |   'RUN_CODE',
2025-07-16 10:16:47.583 |   'DATA_TOOLKIT',
2025-07-16 10:16:47.583 |   'FILE_OPERATION',
2025-07-16 10:16:47.583 |   'ASK_USER_QUESTION',
2025-07-16 10:16:47.583 |   'SCRAPE',
2025-07-16 10:16:47.583 |   'SEARCH',
2025-07-16 10:16:47.583 |   'TASK_MANAGER',
2025-07-16 10:16:47.583 |   'TEXT_ANALYSIS',
2025-07-16 10:16:47.583 |   'WEATHER'
2025-07-16 10:16:47.583 | ]
2025-07-16 10:16:47.583 | PluginRegistry: Registered plugin ids after cache refresh: [
2025-07-16 10:16:47.583 |   'plugin-ACCOMPLISH',
2025-07-16 10:16:47.583 |   'plugin-API_CLIENT',
2025-07-16 10:16:47.583 |   'plugin-CHAT',
2025-07-16 10:16:47.583 |   'plugin-CODE_EXECUTOR',
2025-07-16 10:16:47.583 |   'plugin-DATA_TOOLKIT',
2025-07-16 10:16:47.583 |   'plugin-FILE_OPS_PYTHON',
2025-07-16 10:16:47.583 |   'plugin-ASK_USER_QUESTION',
2025-07-16 10:16:47.583 |   'plugin-SCRAPE',
2025-07-16 10:16:47.583 |   'plugin-SEARCH_PYTHON',
2025-07-16 10:16:47.583 |   'plugin-TASK_MANAGER',
2025-07-16 10:16:47.583 |   'plugin-TEXT_ANALYSIS',
2025-07-16 10:16:47.583 |   'plugin-WEATHER'
2025-07-16 10:16:47.583 | ]
2025-07-16 10:16:47.588 | CapabilitiesManager registered successfully with PostOffice
2025-07-16 10:16:47.597 | Loaded 0 plugins from github repository
2025-07-16 10:16:47.597 | Plugin cache refreshed. Total plugins: 12
2025-07-16 10:16:47.597 | PluginRegistry initialized and cache populated.
2025-07-16 10:16:47.597 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 401. Details: {"message":"Bad credentials","documentation_url":"https://docs.github.com/rest","status":"401"}
2025-07-16 10:16:47.597 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:157:31)
2025-07-16 10:16:47.597 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-16 10:16:47.597 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:364:30)
2025-07-16 10:16:47.597 |     at async PluginRegistry.refreshCache (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:267:37)
2025-07-16 10:16:47.597 |     at async PluginRegistry.initialize (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:113:13)
2025-07-16 10:16:47.597 |     at async CapabilitiesManager.initialize (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:80:21)
2025-07-16 10:16:47.597 |     at async tryInitialize (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:56:17)
2025-07-16 10:16:47.597 | GitHubRepository: Authentication failed. Please check GITHUB_TOKEN and repository permissions.
2025-07-16 10:16:47.598 | PluginRegistry: Registered verbs after cache refresh: [
2025-07-16 10:16:47.598 |   'ACCOMPLISH',
2025-07-16 10:16:47.598 |   'API_CLIENT',
2025-07-16 10:16:47.598 |   'CHAT',
2025-07-16 10:16:47.598 |   'RUN_CODE',
2025-07-16 10:16:47.598 |   'DATA_TOOLKIT',
2025-07-16 10:16:47.598 |   'FILE_OPERATION',
2025-07-16 10:16:47.598 |   'ASK_USER_QUESTION',
2025-07-16 10:16:47.598 |   'SCRAPE',
2025-07-16 10:16:47.598 |   'SEARCH',
2025-07-16 10:16:47.598 |   'TASK_MANAGER',
2025-07-16 10:16:47.598 |   'TEXT_ANALYSIS',
2025-07-16 10:16:47.598 |   'WEATHER'
2025-07-16 10:16:47.598 | ]
2025-07-16 10:16:47.599 | PluginRegistry: Registered plugin ids after cache refresh: [
2025-07-16 10:16:47.599 |   'plugin-ACCOMPLISH',
2025-07-16 10:16:47.599 |   'plugin-API_CLIENT',
2025-07-16 10:16:47.599 |   'plugin-CHAT',
2025-07-16 10:16:47.599 |   'plugin-CODE_EXECUTOR',
2025-07-16 10:16:47.599 |   'plugin-DATA_TOOLKIT',
2025-07-16 10:16:47.599 |   'plugin-FILE_OPS_PYTHON',
2025-07-16 10:16:47.599 |   'plugin-ASK_USER_QUESTION',
2025-07-16 10:16:47.599 |   'plugin-SCRAPE',
2025-07-16 10:16:47.599 |   'plugin-SEARCH_PYTHON',
2025-07-16 10:16:47.599 |   'plugin-TASK_MANAGER',
2025-07-16 10:16:47.599 |   'plugin-TEXT_ANALYSIS',
2025-07-16 10:16:47.599 |   'plugin-WEATHER'
2025-07-16 10:16:47.599 | ]
2025-07-16 10:16:47.599 | [CapabilitiesManager-constructor-dce8889f] CapabilitiesManager.initialize: PluginRegistry initialized.
2025-07-16 10:16:47.601 | [CapabilitiesManager-constructor-dce8889f] CapabilitiesManager.initialize: ConfigManager initialized.
2025-07-16 10:16:47.601 | [CapabilitiesManager-constructor-dce8889f] Setting up express server...
2025-07-16 10:16:47.628 | [CapabilitiesManager-constructor-dce8889f] CapabilitiesManager server listening on port 5060
2025-07-16 10:16:47.629 | [CapabilitiesManager-constructor-dce8889f] CapabilitiesManager server setup complete
2025-07-16 10:16:47.629 | [CapabilitiesManager-constructor-dce8889f] CapabilitiesManager.initialize: CapabilitiesManager initialization completed.
2025-07-16 10:17:10.638 | Connected to RabbitMQ
2025-07-16 10:17:10.652 | Channel created successfully
2025-07-16 10:17:10.652 | RabbitMQ channel ready
2025-07-16 10:17:10.727 | Connection test successful - RabbitMQ connection is stable
2025-07-16 10:17:10.728 | Creating queue: capabilitiesmanager-CapabilitiesManager
2025-07-16 10:17:10.747 | Binding queue to exchange: stage7
2025-07-16 10:17:10.777 | Successfully connected to RabbitMQ and set up queues/bindings
2025-07-16 10:41:49.377 | Created ServiceTokenManager for CapabilitiesManager
2025-07-16 10:41:49.387 | In executeAccomplishPlugin
2025-07-16 10:41:49.388 | LocalRepo: Loading fresh plugin list
2025-07-16 10:41:49.388 | LocalRepo: Loading from  /usr/src/app/services//capabilitiesmanager/src/plugins
2025-07-16 10:41:49.391 | LocalRepo: Loading from  [
2025-07-16 10:41:49.391 |   'ACCOMPLISH',
2025-07-16 10:41:49.391 |   'API_CLIENT',
2025-07-16 10:41:49.391 |   'CHAT',
2025-07-16 10:41:49.391 |   'CODE_EXECUTOR',
2025-07-16 10:41:49.391 |   'DATA_TOOLKIT',
2025-07-16 10:41:49.391 |   'FILE_OPS_PYTHON',
2025-07-16 10:41:49.391 |   'GET_USER_INPUT',
2025-07-16 10:41:49.391 |   'SCRAPE',
2025-07-16 10:41:49.391 |   'SEARCH_PYTHON',
2025-07-16 10:41:49.391 |   'TASK_MANAGER',
2025-07-16 10:41:49.391 |   'TEXT_ANALYSIS',
2025-07-16 10:41:49.391 |   'WEATHER'
2025-07-16 10:41:49.391 | ]
2025-07-16 10:41:49.391 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-16 10:41:49.392 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-16 10:41:49.393 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-16 10:41:49.394 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-16 10:41:49.395 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-16 10:41:49.395 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-16 10:41:49.396 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-16 10:41:49.397 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-16 10:41:49.398 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-16 10:41:49.398 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-16 10:41:49.401 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-16 10:41:49.402 | LocalRepo: Loading from  /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-16 10:41:49.403 | LocalRepo: Locators count 12
2025-07-16 10:41:49.403 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-16 10:41:49.404 | LocalRepository.fetch: Cache hit for id 'plugin-API_CLIENT' at /usr/src/app/services/capabilitiesmanager/src/plugins/API_CLIENT/manifest.json
2025-07-16 10:41:49.404 | LocalRepository.fetch: Cache hit for id 'plugin-CHAT' at /usr/src/app/services/capabilitiesmanager/src/plugins/CHAT/manifest.json
2025-07-16 10:41:49.405 | LocalRepository.fetch: Cache hit for id 'plugin-CODE_EXECUTOR' at /usr/src/app/services/capabilitiesmanager/src/plugins/CODE_EXECUTOR/manifest.json
2025-07-16 10:41:49.405 | LocalRepository.fetch: Cache hit for id 'plugin-DATA_TOOLKIT' at /usr/src/app/services/capabilitiesmanager/src/plugins/DATA_TOOLKIT/manifest.json
2025-07-16 10:41:49.405 | LocalRepository.fetch: Cache hit for id 'plugin-FILE_OPS_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/FILE_OPS_PYTHON/manifest.json
2025-07-16 10:41:49.406 | LocalRepository.fetch: Cache hit for id 'plugin-ASK_USER_QUESTION' at /usr/src/app/services/capabilitiesmanager/src/plugins/GET_USER_INPUT/manifest.json
2025-07-16 10:41:49.406 | LocalRepository.fetch: Cache hit for id 'plugin-SCRAPE' at /usr/src/app/services/capabilitiesmanager/src/plugins/SCRAPE/manifest.json
2025-07-16 10:41:49.407 | LocalRepository.fetch: Cache hit for id 'plugin-SEARCH_PYTHON' at /usr/src/app/services/capabilitiesmanager/src/plugins/SEARCH_PYTHON/manifest.json
2025-07-16 10:41:49.407 | LocalRepository.fetch: Cache hit for id 'plugin-TASK_MANAGER' at /usr/src/app/services/capabilitiesmanager/src/plugins/TASK_MANAGER/manifest.json
2025-07-16 10:41:49.408 | LocalRepository.fetch: Cache hit for id 'plugin-TEXT_ANALYSIS' at /usr/src/app/services/capabilitiesmanager/src/plugins/TEXT_ANALYSIS/manifest.json
2025-07-16 10:41:49.408 | LocalRepository.fetch: Cache hit for id 'plugin-WEATHER' at /usr/src/app/services/capabilitiesmanager/src/plugins/WEATHER/manifest.json
2025-07-16 10:41:50.186 | Error: GitHub API Error for GET https://api.github.com/repos/cpravetz/s7plugins/contents/plugins. Status: 401. Details: {"message":"Bad credentials","documentation_url":"https://docs.github.com/rest","status":"401"}
2025-07-16 10:41:50.186 |     at GitHubRepository.makeGitHubRequest (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:157:31)
2025-07-16 10:41:50.186 |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-16 10:41:50.186 |     at async GitHubRepository.list (/usr/src/app/marketplace/dist/repositories/GitHubRepository.js:364:30)
2025-07-16 10:41:50.186 |     at async PluginMarketplace.getAvailablePluginsStr (/usr/src/app/marketplace/dist/PluginMarketplace.js:356:34)
2025-07-16 10:41:50.186 |     at async PluginRegistry.getAvailablePluginsStr (/usr/src/app/services/capabilitiesmanager/dist/utils/pluginRegistry.js:302:20)
2025-07-16 10:41:50.186 |     at async CapabilitiesManager.executeAccomplishPlugin (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:1210:35)
2025-07-16 10:41:50.186 |     at async CapabilitiesManager.executeActionVerb (/usr/src/app/services/capabilitiesmanager/dist/CapabilitiesManager.js:393:47)
2025-07-16 10:41:50.186 | GitHubRepository: Authentication failed. Please check GITHUB_TOKEN and repository permissions.
2025-07-16 10:41:50.186 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.executeAccomplishPlugin: Plugins string for ACCOMPLISH: - DELEGATE: Create sub-agents with goals of their own.
2025-07-16 10:41:50.186 | - THINK: - sends prompts to the chat function...
2025-07-16 10:41:50.186 | PluginRegistry.fetchOneByVerb called for verb: ACCOMPLISH
2025-07-16 10:41:50.187 | LocalRepository.fetch: Cache hit for id 'plugin-ACCOMPLISH' at /usr/src/app/services/capabilitiesmanager/src/plugins/ACCOMPLISH/manifest.json
2025-07-16 10:41:50.192 | Using inline plugin path for plugin-ACCOMPLISH (ACCOMPLISH): /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH
2025-07-16 10:41:50.192 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.executePlugin: Executing plugin plugin-ACCOMPLISH v1.0.0 (ACCOMPLISH) at /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH
2025-07-16 10:41:50.225 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.executePythonPlugin: Python execution - Main file path: /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/main.py, Root path: /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH
2025-07-16 10:41:50.260 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.ensurePythonDependencies: Found python executable with command: python3 --version
2025-07-16 10:41:50.260 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.ensurePythonDependencies: Creating virtual environment at /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv.
2025-07-16 10:41:50.260 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.ensurePythonDependencies: Running command: python3 -m venv "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv"
2025-07-16 10:41:58.807 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.ensurePythonDependencies: Upgrading pip with command: "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv/bin/pip" install --upgrade pip
2025-07-16 10:42:08.894 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.ensurePythonDependencies: Installing requirements with command: "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv/bin/pip" install -r "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt"
2025-07-16 10:42:12.749 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.ensurePythonDependencies: Python dependency installation stdout: Collecting requests>=2.28.0 (from -r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-16 10:42:12.749 |   Downloading requests-2.32.4-py3-none-any.whl.metadata (4.9 kB)
2025-07-16 10:42:12.749 | Collecting charset_normalizer<4,>=2 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-16 10:42:12.749 |   Downloading charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_x86_64.whl.metadata (35 kB)
2025-07-16 10:42:12.749 | Collecting idna<4,>=2.5 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-16 10:42:12.749 |   Downloading idna-3.10-py3-none-any.whl.metadata (10 kB)
2025-07-16 10:42:12.749 | Collecting urllib3<3,>=1.21.1 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-16 10:42:12.749 |   Downloading urllib3-2.5.0-py3-none-any.whl.metadata (6.5 kB)
2025-07-16 10:42:12.749 | Collecting certifi>=2017.4.17 (from requests>=2.28.0->-r /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/requirements.txt (line 1))
2025-07-16 10:42:12.749 |   Downloading certifi-2025.7.14-py3-none-any.whl.metadata (2.4 kB)
2025-07-16 10:42:12.749 | Downloading requests-2.32.4-py3-none-any.whl (64 kB)
2025-07-16 10:42:12.749 | Downloading charset_normalizer-3.4.2-cp312-cp312-musllinux_1_2_x86_64.whl (149 kB)
2025-07-16 10:42:12.749 | Downloading idna-3.10-py3-none-any.whl (70 kB)
2025-07-16 10:42:12.749 | Downloading urllib3-2.5.0-py3-none-any.whl (129 kB)
2025-07-16 10:42:12.749 | Downloading certifi-2025.7.14-py3-none-any.whl (162 kB)
2025-07-16 10:42:12.749 | Installing collected packages: urllib3, idna, charset_normalizer, certifi, requests
2025-07-16 10:42:12.749 | 
2025-07-16 10:42:12.749 | Successfully installed certifi-2025.7.14 charset_normalizer-3.4.2 idna-3.10 requests-2.32.4 urllib3-2.5.0
2025-07-16 10:42:12.749 | 
2025-07-16 10:42:12.750 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.ensurePythonDependencies: Python dependencies processed successfully for /usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH. Marker file updated.
2025-07-16 10:42:12.751 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.executePythonPlugin: Executing Python command: echo "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" | base64 -d | "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/venv/bin/python" "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH/main.py" "/usr/src/app/services/capabilitiesmanager/dist/plugins/ACCOMPLISH"
2025-07-16 10:42:12.751 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.executePythonPlugin: Piping inputsJsonString to Python plugin: [["goal",{"inputName":"goal","value":"Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.","valueType":"string","args":{}}],["verbToAvoid",{"inputName":"verbToAvoid","value":"EXECUTE","valueType":"string","args":{}}],["available_plugins",{"inputName":"available_plugins","value":"- DELEGATE: Create sub-agents with goals of their own.\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\n- CHAT: Manages interactive chat sessions with the user.\n- RUN_CODE: Executes code snippets in a sandboxed environment.\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\n- FILE_OPERATION: Provides services for file operations: read, write, append\n- ASK_USER_QUESTION: Requests input from the user\n- SCRAPE: Scrapes content from a given URL\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\n- WEATHER: Fetches current weather information for a specified location","valueType":"string","args":{}}],["postOffice_url",{"inputName":"postOffice_url","value":"postoffice:5020","valueType":"string","args":{}}],["brain_url",{"inputName":"brain_url","value":"brain:5070","valueType":"string","args":{}}],["librarian_url",{"inputName":"librarian_url","value":"librarian:5040","valueType":"string","args":{}}],["__auth_token",{"inputName":"__auth_token","value":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","valueType":"string","args":{"token":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}],["__brain_auth_token",{"inputName":"__brain_auth_token","value":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","valueType":"string","args":{"token":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}],["token",{"inputName":"token","value":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","valueType":"string","args":{"token":"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}}]]
2025-07-16 10:42:16.109 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.executePythonPlugin: Raw stdout from Python plugin ACCOMPLISH v1.0.0:
2025-07-16 10:42:16.109 | [{"success": false, "name": "error", "resultType": "ERROR", "resultDescription": "Failed to get response from Brain service.", "result": {"logs": "2025-07-16 14:42:13,086 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}\n2025-07-16 14:42:13,087 - INFO - [ACCOMPLISH] Using provided plugin context or fallback\n2025-07-16 14:42:13,087 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-16 14:42:13,087 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-16 14:42:14,691 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n2025-07-16 14:42:14,692 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-16 14:42:14,692 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-16 14:42:16,061 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n"}, "error": "Brain service unavailable."}]
2025-07-16 10:42:16.109 | 
2025-07-16 10:42:16.110 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.executePythonPlugin: Raw stderr from Python plugin ACCOMPLISH v1.0.0:
2025-07-16 10:42:16.110 | 2025-07-16 14:42:13,086 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}
2025-07-16 10:42:16.110 | 2025-07-16 14:42:13,087 - INFO - [ACCOMPLISH] Using provided plugin context or fallback
2025-07-16 10:42:16.110 | 2025-07-16 14:42:13,087 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {"inputName": "value"}, trueSteps[], falseSteps[])\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {"inputName": "value"}, steps[])\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {"inputName": "value"}, steps[])\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\n- CHAT: Manages interactive chat sessions with the user.\n- RUN_CODE: Executes code snippets in a sandboxed environment.\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\n- FILE_OPERATION: Provides services for file operations: read, write, append\n- ASK_USER_QUESTION: Requests input from the user\n- SCRAPE: Scrapes content from a given URL\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\n- WEATHER: Fetches current weather information for a specified location'
2025-07-16 10:42:16.110 | 2025-07-16 14:42:13,087 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'
2025-07-16 10:42:16.110 | 2025-07-16 14:42:14,691 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat
2025-07-16 10:42:16.110 | 2025-07-16 14:42:14,692 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {"inputName": "value"}, trueSteps[], falseSteps[])\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {"inputName": "value"}, steps[])\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {"inputName": "value"}, steps[])\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\n- CHAT: Manages interactive chat sessions with the user.\n- RUN_CODE: Executes code snippets in a sandboxed environment.\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\n- FILE_OPERATION: Provides services for file operations: read, write, append\n- ASK_USER_QUESTION: Requests input from the user\n- SCRAPE: Scrapes content from a given URL\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\n- WEATHER: Fetches current weather information for a specified location'
2025-07-16 10:42:16.110 | 2025-07-16 14:42:14,692 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'
2025-07-16 10:42:16.110 | 2025-07-16 14:42:16,061 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat
2025-07-16 10:42:16.110 | 
2025-07-16 10:42:16.110 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.validatePythonOutput: Validating Python output for ACCOMPLISH v1.0.0. Received stdout:
2025-07-16 10:42:16.110 | [{"success": false, "name": "error", "resultType": "ERROR", "resultDescription": "Failed to get response from Brain service.", "result": {"logs": "2025-07-16 14:42:13,086 - INFO - Execute method called with goal: {'inputName': 'goal', 'value': 'Find me a job. I will upload my resume in a moment. Use that and my linkedin profile www.linkedin.com/in/chrispravetz to figure out what sort of jobs I should pursue and make a plan to find those jobs, both published and unpublished. Create a list of people or organizations I should contact and provide draft messages for each. Create lists of posted jobs I should apply to and create cover letters and customized resumes for each. Figure out a way to continue to monitor the internet for future job posts that match the target jobs.', 'valueType': 'string', 'args': {}}\n2025-07-16 14:42:13,087 - INFO - [ACCOMPLISH] Using provided plugin context or fallback\n2025-07-16 14:42:13,087 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-16 14:42:13,087 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-16 14:42:14,691 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n2025-07-16 14:42:14,692 - INFO - [ACCOMPLISH] Received available_plugins_str: '- DELEGATE: Create sub-agents with goals of their own.\\n- THINK: - sends prompts to the chat function of the LLMs attached to the system in order to generate content from a conversation.(required input: prompt) (optional inputs: optimization (cost|accuracy|creativity|speed|continuity), ConversationType) accuracy is the default optimization\\n- GENERATE: - uses LLM services to generate content from a prompt or other content. Services include image creation, audio transcription, image editing, etc. (required input: ConversationType) (optional inputs: modelName, optimization, prompt, file, audio, video, image...)\\n- DECIDE: - Conditional branching based on a condition (required inputs: condition: {\"inputName\": \"value\"}, trueSteps[], falseSteps[])\\n- WHILE: - Repeat steps while a condition is true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- UNTIL: - Repeat steps until a condition becomes true (required inputs: condition: {\"inputName\": \"value\"}, steps[])\\n- SEQUENCE: - Execute steps in strict sequential order / no concurrency (required inputs: steps[])\\n- TIMEOUT: - Set a timeout for a group of steps (required inputs: timeout, steps[])\\n- REPEAT: - Repeat steps a specific number of times (required inputs: count, steps[])\\n- FOREACH: - Iterate over an array and execute steps for each item (required inputs: array, steps[plan])\\n- ACCOMPLISH: Takes a goal and either creates a solution for the goal, recommends development of a new plugin, or creates a detailed plan to create the solution\\n- API_CLIENT: A generic interface for interacting with third-party RESTful APIs.\\n- CHAT: Manages interactive chat sessions with the user.\\n- RUN_CODE: Executes code snippets in a sandboxed environment.\\n- DATA_TOOLKIT: A set of tools for processing and manipulating structured data formats like JSON, CSV, and SQL.\\n- FILE_OPERATION: Provides services for file operations: read, write, append\\n- ASK_USER_QUESTION: Requests input from the user\\n- SCRAPE: Scrapes content from a given URL\\n- SEARCH: Searches the internet using SearchXNG for a given term and returns a list of links\\n- TASK_MANAGER: A plugin for self-planning, creating, and managing tasks and subtasks.\\n- TEXT_ANALYSIS: Performs comprehensive text analysis including statistics, keywords, and sentiment\\n- WEATHER: Fetches current weather information for a specified location'\n2025-07-16 14:42:14,692 - INFO - [ACCOMPLISH] Received mission_context_str: 'No overall mission context provided.'\n2025-07-16 14:42:16,061 - ERROR - Brain query failed: 500 Server Error: Internal Server Error for url: http://brain:5070/chat\n"}, "error": "Brain service unavailable."}]
2025-07-16 10:42:16.110 | 
2025-07-16 10:42:16.110 | [f397d4d5-c01b-407b-a94b-b0620e7d3bbf] CapabilitiesManager.validatePythonOutput: Python plugin output parsed and validated successfully for ACCOMPLISH v1.0.0