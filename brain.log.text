2025-07-16 10:16:38.854 | RSA private key for plugin signing not found (this is normal for most services)
2025-07-16 10:16:38.895 | Loaded RSA public key for plugin verification
2025-07-16 10:16:39.000 | Attempting to connect to RabbitMQ (attempt 1/20)...
2025-07-16 10:16:39.000 | Using RabbitMQ URL: amqp://stage7:stage7password@rabbitmq:5672
2025-07-16 10:16:39.000 | Attempting to connect to RabbitMQ host: rabbitmq
2025-07-16 10:16:39.000 | Connecting to RabbitMQ at amqp://stage7:stage7password@rabbitmq:5672
2025-07-16 10:16:39.000 | Attempting to register with <PERSON> (attempt 1/10)...
2025-07-16 10:16:39.006 | Using Consul URL: consul:8500
2025-07-16 10:16:39.262 | Brain service listening at http://0.0.0.0:5070
2025-07-16 10:16:39.263 | [shouldBypassAuth] Bypassing auth for auth path: /v1/agent/service/register (matched /register)
2025-07-16 10:16:39.359 | Anthropic Service created, <PERSON><PERSON><PERSON><PERSON> starts sk-ant
2025-07-16 10:16:39.359 | Loaded service: AntService
2025-07-16 10:16:39.371 | GG Gemini Service created, ApiKey starts AIzaSy
2025-07-16 10:16:39.371 | Loaded service: GGService
2025-07-16 10:16:39.427 | Gemini Service created, ApiKey starts AIzaSy
2025-07-16 10:16:39.427 | Loaded service: gemini
2025-07-16 10:16:39.457 | Groq Service created, ApiKey starts gsk_m0
2025-07-16 10:16:39.463 | GroqService initialized with API key: Set (length: 56)
2025-07-16 10:16:39.463 | Loaded service: groq
2025-07-16 10:16:39.503 | Huggingface Service created with API key: Set (length: 37)
2025-07-16 10:16:39.503 | Loaded service: HFService
2025-07-16 10:16:39.512 | Mistral Service created, ApiKey starts AhDwC8
2025-07-16 10:16:39.512 | Loaded service: MistralService
2025-07-16 10:16:39.519 | OpenAI Service created, ApiKey starts sk-LaE
2025-07-16 10:16:39.519 | Loaded service: OAService
2025-07-16 10:16:39.525 | OpenRouter Service created, ApiKey starts sk-or-
2025-07-16 10:16:39.525 | Loaded service: ORService
2025-07-16 10:16:39.528 | Openweb Service created, ApiKey starts eyJhbG
2025-07-16 10:16:39.528 | Using default OpenWebUI URL: https://knllm.dusdusdusd.com
2025-07-16 10:16:39.530 | Loaded service: OWService
2025-07-16 10:16:39.537 | modelManager Loaded 9 services.
2025-07-16 10:16:39.543 | Loaded interface: anthropic
2025-07-16 10:16:39.543 | Loaded interface: gemini
2025-07-16 10:16:40.917 | Loaded interface: groq
2025-07-16 10:16:40.990 | Loaded interface: huggingface
2025-07-16 10:16:40.997 | Loaded interface: mistral
2025-07-16 10:16:41.005 | Loaded interface: openai
2025-07-16 10:16:41.017 | Loaded interface: openrouter
2025-07-16 10:16:41.021 | OpenWebUIInterface initialized with DEFAULT_TIMEOUT: 300000ms
2025-07-16 10:16:41.021 | Loaded interface: openwebui
2025-07-16 10:16:41.021 | modelManager Loaded 8 interfaces.
2025-07-16 10:16:41.042 | Loaded model: hf/meta-llama/llama-3.2-3b-instruct
2025-07-16 10:16:41.050 | Loaded model: suno/bark
2025-07-16 10:16:41.053 | Loaded model: anthropic/claude-3-haiku-20240307
2025-07-16 10:16:41.062 | Loaded model: anthropic/claude-3-haiku-20240307
2025-07-16 10:16:41.064 | Loaded model: anthropic/claude-2
2025-07-16 10:16:41.066 | Loaded model: codellama/CodeLlama-34b-Instruct-hf
2025-07-16 10:16:41.068 | Loaded model: THUDM/cogvlm-chat-hf
2025-07-16 10:16:41.069 | Loaded model: openai/dall-e-2
2025-07-16 10:16:41.089 | Loaded model: openai/dall-e-3
2025-07-16 10:16:41.096 | Loaded model: deepseek-ai/DeepSeek-R1
2025-07-16 10:16:41.100 | Loaded model: openai/whisper-large-v3
2025-07-16 10:16:41.100 | Loaded model: google/gemini-2.0-flash-lite
2025-07-16 10:16:41.102 | Loaded model: google/gemini-2.5-flash
2025-07-16 10:16:41.106 | Loaded model: google/gemma-3-27b-it
2025-07-16 10:16:41.119 | Loaded model: openai/gpt-4.1-nano
2025-07-16 10:16:41.135 | Loaded model: openai/gpt-4-vision-preview
2025-07-16 10:16:41.139 | Loaded model: nousresearch/hermes-3-llama-3.1-405b
2025-07-16 10:16:41.164 | KNLLMModel initialized with OpenWebUI interface
2025-07-16 10:16:41.164 | Loaded model: openweb/knownow
2025-07-16 10:16:41.164 | Loaded model: liquid/lfm-40b
2025-07-16 10:16:41.164 | Loaded model: meta-llama/llama-3.2-11b-vision-instruct
2025-07-16 10:16:41.164 | GroqService availability check: Available
2025-07-16 10:16:41.164 | GroqService API key: Set (length: 56)
2025-07-16 10:16:41.164 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 10:16:41.164 | GroqService ready state: Ready
2025-07-16 10:16:41.164 | GroqService is available and ready to use.
2025-07-16 10:16:41.164 | Loaded model: groq/llama-4
2025-07-16 10:16:41.168 | Loaded model: meta-llama/Llama-2-70b-chat-hf
2025-07-16 10:16:41.188 | Loaded model: liuhaotian/llava-v1.5-13b
2025-07-16 10:16:41.188 | Loaded model: microsoft/Phi-3.5-vision-instruct
2025-07-16 10:16:41.193 | MistralService availability check: Available
2025-07-16 10:16:41.193 | MistralService API key: Set
2025-07-16 10:16:41.193 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 10:16:41.193 | MistralService is available and ready to use.
2025-07-16 10:16:41.193 | Loaded model: mistral/mistral-small-latest
2025-07-16 10:16:41.198 | Loaded model: mistralai/Mistral-Nemo-Instruct-2407
2025-07-16 10:16:41.205 | Loaded model: facebook/musicgen-large
2025-07-16 10:16:41.216 | MistralService availability check: Available
2025-07-16 10:16:41.216 | MistralService API key: Set
2025-07-16 10:16:41.216 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 10:16:41.216 | MistralService is available and ready to use.
2025-07-16 10:16:41.216 | Loaded model: mistral/pixtral-12B-2409
2025-07-16 10:16:41.227 | GroqService availability check: Available
2025-07-16 10:16:41.227 | GroqService API key: Set (length: 56)
2025-07-16 10:16:41.227 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 10:16:41.227 | GroqService ready state: Ready
2025-07-16 10:16:41.227 | GroqService is available and ready to use.
2025-07-16 10:16:41.227 | Loaded model: groq/qwen-qwq-32b
2025-07-16 10:16:41.229 | Loaded model: facebook/seamless-m4t-large
2025-07-16 10:16:41.236 | Loaded model: stabilityai/stable-diffusion-xl-base-1.0
2025-07-16 10:16:41.237 | Loaded model: bigcode/starcoder
2025-07-16 10:16:41.238 | Loaded model: openai/tts
2025-07-16 10:16:41.251 | Loaded model: openai/whisper-large-v3
2025-07-16 10:16:41.253 | Loaded model: openai/whisper
2025-07-16 10:16:41.253 | modelManager Loaded 33 models.
2025-07-16 10:16:41.276 | [shouldBypassAuth] Bypassing auth for auth path: /registerComponent (matched /register)
2025-07-16 10:16:41.426 | [Brain] Attempting to restore model performance data from Librarian...
2025-07-16 10:16:41.465 | [AuthenticatedAxios] Request vh0kaqfj1xg: Failed after 24ms: {
2025-07-16 10:16:41.465 |   status: undefined,
2025-07-16 10:16:41.465 |   statusText: undefined,
2025-07-16 10:16:41.465 |   data: undefined,
2025-07-16 10:16:41.465 |   url: 'http://librarian:5040/loadData/model-performance-data'
2025-07-16 10:16:41.465 | }
2025-07-16 10:16:42.494 | [Brain] Error restoring performance data from Librarian: connect ECONNREFUSED 172.19.0.9:5040
2025-07-16 10:16:47.457 | Service Brain registered with Consul
2025-07-16 10:16:47.458 | Successfully registered Brain with Consul
2025-07-16 10:16:47.474 | Brain registered successfully with PostOffice
2025-07-16 10:16:47.528 | Created ServiceTokenManager for Brain
2025-07-16 10:17:09.293 | Connected to RabbitMQ
2025-07-16 10:17:09.301 | Channel created successfully
2025-07-16 10:17:09.301 | RabbitMQ channel ready
2025-07-16 10:17:09.357 | Connection test successful - RabbitMQ connection is stable
2025-07-16 10:17:09.357 | Creating queue: brain-Brain
2025-07-16 10:17:09.368 | Binding queue to exchange: stage7
2025-07-16 10:17:09.375 | Successfully connected to RabbitMQ and set up queues/bindings
2025-07-16 10:42:13.158 | [Brain Chat] Request d0750089-d613-44c2-8dfc-39a22c210f64 received
2025-07-16 10:42:13.159 | Selecting model for optimization: accuracy, conversationType: TextToCode
2025-07-16 10:42:13.159 | **** CACHE MISS **** No cached result for key: accuracy-TextToCode
2025-07-16 10:42:13.159 | Cache miss or expired. Selecting model from scratch.
2025-07-16 10:42:13.159 | Total models loaded: 33
2025-07-16 10:42:13.161 | GroqService availability check: Available
2025-07-16 10:42:13.161 | GroqService API key: Set (length: 56)
2025-07-16 10:42:13.161 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 10:42:13.161 | GroqService ready state: Ready
2025-07-16 10:42:13.161 | GroqService is available and ready to use.
2025-07-16 10:42:13.161 | MistralService availability check: Available
2025-07-16 10:42:13.162 | MistralService API key: Set
2025-07-16 10:42:13.162 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 10:42:13.162 | MistralService is available and ready to use.
2025-07-16 10:42:13.162 | MistralService availability check: Available
2025-07-16 10:42:13.162 | MistralService API key: Set
2025-07-16 10:42:13.162 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 10:42:13.162 | MistralService is available and ready to use.
2025-07-16 10:42:13.162 | GroqService availability check: Available
2025-07-16 10:42:13.162 | GroqService API key: Set (length: 56)
2025-07-16 10:42:13.162 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 10:42:13.162 | GroqService ready state: Ready
2025-07-16 10:42:13.162 | GroqService is available and ready to use.
2025-07-16 10:42:13.163 | Model anthropic/claude-3-haiku-20240307 score calculation: base=90, adjusted=90, reliability=0, final=90
2025-07-16 10:42:13.163 | Model anthropic/claude-2 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 10:42:13.163 | Model codellama/CodeLlama-34b-Instruct-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 10:42:13.163 | Model deepseek-ai/DeepSeek-R1 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 10:42:13.163 | Model google/gemini-2.0-flash-lite score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 10:42:13.163 | Model google/gemini-2.5-flash score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 10:42:13.163 | Model openai/gpt-4.1-nano score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 10:42:13.163 | Model openai/gpt-4-vision-preview score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 10:42:13.163 | Model openweb/knownow score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 10:42:13.163 | Model groq/llama-4 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 10:42:13.164 | Model meta-llama/Llama-2-70b-chat-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 10:42:13.164 | Model mistral/mistral-small-latest score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 10:42:13.164 | Model mistralai/Mistral-Nemo-Instruct-2407 score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 10:42:13.164 | Model mistral/pixtral-12B-2409 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 10:42:13.164 | Model groq/qwen-qwq-32b score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 10:42:13.164 | Model bigcode/starcoder score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 10:42:13.164 | Using score-based model selection. Top model: anthropic/claude-2
2025-07-16 10:42:13.164 | Selected model anthropic/claude-2 for accuracy optimization and conversation type TextToCode
2025-07-16 10:42:13.164 | [Brain Chat] Using model anthropic/claude-2
2025-07-16 10:42:13.165 | [ModelManager] Tracking model request: 94e055dd-3c8b-4e79-97db-402645bbd9f7 for model anthropic/claude-2, conversation type TextToCode
2025-07-16 10:42:13.165 | [ModelManager] Active requests count: 1
2025-07-16 10:42:13.167 | Starting trimMessages
2025-07-16 10:42:14.688 | [ModelManager] Tracking model response for request d0750089-d613-44c2-8dfc-39a22c210f64, success: false, token count: 0, isRetry: undefined
2025-07-16 10:42:14.688 | Clearing model selection cache
2025-07-16 10:42:14.688 | Selecting model for optimization: accuracy, conversationType: TextToCode
2025-07-16 10:42:14.688 | **** CACHE MISS **** No cached result for key: accuracy-TextToCode
2025-07-16 10:42:14.688 | Cache miss or expired. Selecting model from scratch.
2025-07-16 10:42:14.688 | Total models loaded: 33
2025-07-16 10:42:14.688 | [Brain] Model anthropic/claude-2 failed: Connection error.
2025-07-16 10:42:14.688 | No active request found for request ID d0750089-d613-44c2-8dfc-39a22c210f64
2025-07-16 10:42:14.688 | GroqService availability check: Available
2025-07-16 10:42:14.688 | GroqService API key: Set (length: 56)
2025-07-16 10:42:14.688 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 10:42:14.688 | GroqService ready state: Ready
2025-07-16 10:42:14.688 | GroqService is available and ready to use.
2025-07-16 10:42:14.688 | MistralService availability check: Available
2025-07-16 10:42:14.688 | MistralService API key: Set
2025-07-16 10:42:14.688 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 10:42:14.689 | MistralService is available and ready to use.
2025-07-16 10:42:14.689 | MistralService availability check: Available
2025-07-16 10:42:14.689 | MistralService API key: Set
2025-07-16 10:42:14.689 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 10:42:14.689 | MistralService is available and ready to use.
2025-07-16 10:42:14.689 | GroqService availability check: Available
2025-07-16 10:42:14.689 | GroqService API key: Set (length: 56)
2025-07-16 10:42:14.689 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 10:42:14.689 | GroqService ready state: Ready
2025-07-16 10:42:14.689 | GroqService is available and ready to use.
2025-07-16 10:42:14.689 | Model anthropic/claude-3-haiku-20240307 score calculation: base=90, adjusted=90, reliability=0, final=90
2025-07-16 10:42:14.689 | Model anthropic/claude-2 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 10:42:14.689 | Model codellama/CodeLlama-34b-Instruct-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 10:42:14.689 | Model deepseek-ai/DeepSeek-R1 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 10:42:14.689 | Model google/gemini-2.0-flash-lite score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 10:42:14.689 | Model google/gemini-2.5-flash score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 10:42:14.689 | Model openai/gpt-4.1-nano score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 10:42:14.689 | Model openai/gpt-4-vision-preview score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 10:42:14.689 | Model openweb/knownow score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 10:42:14.689 | Model groq/llama-4 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 10:42:14.689 | Model meta-llama/Llama-2-70b-chat-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 10:42:14.689 | Model mistral/mistral-small-latest score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 10:42:14.689 | Model mistralai/Mistral-Nemo-Instruct-2407 score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 10:42:14.689 | Model mistral/pixtral-12B-2409 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 10:42:14.689 | Model groq/qwen-qwq-32b score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 10:42:14.689 | Model bigcode/starcoder score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 10:42:14.690 | [Brain Chat] Error: Connection error.
2025-07-16 10:42:14.690 | Using score-based model selection. Top model: anthropic/claude-2
2025-07-16 10:42:14.690 | Selected model anthropic/claude-2 for accuracy optimization and conversation type TextToCode
2025-07-16 10:42:14.696 | [Brain Chat] Request 2c9b7384-080d-40f2-9f98-c7ba45c07de4 received
2025-07-16 10:42:14.697 | Selecting model for optimization: accuracy, conversationType: TextToCode
2025-07-16 10:42:14.697 | **** CACHE HIT **** Using cached model selection result: anthropic/claude-2
2025-07-16 10:42:14.697 | Cache age: 0 seconds
2025-07-16 10:42:14.697 | [Brain Chat] Using model anthropic/claude-2
2025-07-16 10:42:14.697 | [ModelManager] Tracking model request: ded1836e-98f0-4a4c-8bba-cba5d5abbe0e for model anthropic/claude-2, conversation type TextToCode
2025-07-16 10:42:14.697 | [ModelManager] Active requests count: 2
2025-07-16 10:42:14.697 | Starting trimMessages
2025-07-16 10:42:16.060 | [Brain] Model anthropic/claude-2 failed: Connection error.
2025-07-16 10:42:16.060 | No active request found for request ID 2c9b7384-080d-40f2-9f98-c7ba45c07de4
2025-07-16 10:42:16.060 | [ModelManager] Tracking model response for request 2c9b7384-080d-40f2-9f98-c7ba45c07de4, success: false, token count: 0, isRetry: undefined
2025-07-16 10:42:16.060 | Clearing model selection cache
2025-07-16 10:42:16.060 | Selecting model for optimization: accuracy, conversationType: TextToCode
2025-07-16 10:42:16.060 | **** CACHE MISS **** No cached result for key: accuracy-TextToCode
2025-07-16 10:42:16.060 | Cache miss or expired. Selecting model from scratch.
2025-07-16 10:42:16.060 | Total models loaded: 33
2025-07-16 10:42:16.060 | GroqService availability check: Available
2025-07-16 10:42:16.060 | GroqService API key: Set (length: 56)
2025-07-16 10:42:16.060 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 10:42:16.060 | GroqService ready state: Ready
2025-07-16 10:42:16.060 | GroqService is available and ready to use.
2025-07-16 10:42:16.060 | MistralService availability check: Available
2025-07-16 10:42:16.060 | MistralService API key: Set
2025-07-16 10:42:16.060 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 10:42:16.060 | MistralService is available and ready to use.
2025-07-16 10:42:16.060 | MistralService availability check: Available
2025-07-16 10:42:16.060 | MistralService API key: Set
2025-07-16 10:42:16.060 | MistralService API URL: https://api.mistral.ai/v1
2025-07-16 10:42:16.060 | MistralService is available and ready to use.
2025-07-16 10:42:16.060 | GroqService availability check: Available
2025-07-16 10:42:16.060 | GroqService API key: Set (length: 56)
2025-07-16 10:42:16.060 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-16 10:42:16.060 | GroqService ready state: Ready
2025-07-16 10:42:16.060 | GroqService is available and ready to use.
2025-07-16 10:42:16.060 | Model anthropic/claude-3-haiku-20240307 score calculation: base=90, adjusted=90, reliability=0, final=90
2025-07-16 10:42:16.060 | Model anthropic/claude-2 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 10:42:16.060 | Model codellama/CodeLlama-34b-Instruct-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 10:42:16.060 | Model deepseek-ai/DeepSeek-R1 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 10:42:16.060 | Model google/gemini-2.0-flash-lite score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 10:42:16.060 | Model google/gemini-2.5-flash score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 10:42:16.060 | Model openai/gpt-4.1-nano score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 10:42:16.060 | Model openai/gpt-4-vision-preview score calculation: base=92, adjusted=92, reliability=0, final=92
2025-07-16 10:42:16.060 | Model openweb/knownow score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 10:42:16.060 | Model groq/llama-4 score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 10:42:16.060 | Model meta-llama/Llama-2-70b-chat-hf score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 10:42:16.060 | Model mistral/mistral-small-latest score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 10:42:16.060 | Model mistralai/Mistral-Nemo-Instruct-2407 score calculation: base=80, adjusted=80, reliability=0, final=80
2025-07-16 10:42:16.060 | Model mistral/pixtral-12B-2409 score calculation: base=88, adjusted=88, reliability=0, final=88
2025-07-16 10:42:16.060 | Model groq/qwen-qwq-32b score calculation: base=95, adjusted=95, reliability=0, final=95
2025-07-16 10:42:16.060 | Model bigcode/starcoder score calculation: base=85, adjusted=85, reliability=0, final=85
2025-07-16 10:42:16.060 | Using score-based model selection. Top model: anthropic/claude-2
2025-07-16 10:42:16.060 | Selected model anthropic/claude-2 for accuracy optimization and conversation type TextToCode
2025-07-16 10:42:16.060 | [Brain Chat] Error: Connection error.